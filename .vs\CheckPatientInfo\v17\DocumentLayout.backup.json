{"Version": 1, "WorkspaceRootPath": "E:\\vs_work\\CheckPatientInfo\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|e:\\vs_work\\checkpatientinfo\\convergepacs.ini||{3B902123-F8A7-4915-9F01-361F908088D0}", "RelativeMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|solutionrelative:convergepacs.ini||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|e:\\vs_work\\checkpatientinfo\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2A847972-25E8-429C-8DAE-F7C73861DE3B}|CheckPatientInfo.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{00000000-0000-0000-0000-000000000000}|<Solution>|CheckPatientInfo||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ConvergePACS.ini", "DocumentMoniker": "E:\\vs_work\\CheckPatientInfo\\ConvergePACS.ini", "RelativeDocumentMoniker": "ConvergePACS.ini", "ToolTip": "E:\\vs_work\\CheckPatientInfo\\ConvergePACS.ini", "RelativeToolTip": "ConvergePACS.ini", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.002768|", "WhenOpened": "2025-08-01T14:21:32.711Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c64b9c2-e352-428e-a56d-0ace190b99a6}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "CheckPatientInfo", "DocumentMoniker": "E:\\vs_work\\CheckPatientInfo\\CheckPatientInfo.csproj", "RelativeDocumentMoniker": "CheckPatientInfo.csproj", "ToolTip": "E:\\vs_work\\CheckPatientInfo\\CheckPatientInfo.csproj", "RelativeToolTip": "CheckPatientInfo.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-31T02:20:56.297Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "E:\\vs_work\\CheckPatientInfo\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "E:\\vs_work\\CheckPatientInfo\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAACMAAAAAAAAAAADwvwACAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:17:19.616Z", "EditorCaption": ""}]}]}]}